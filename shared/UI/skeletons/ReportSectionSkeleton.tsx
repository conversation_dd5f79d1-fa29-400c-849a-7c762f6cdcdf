import React from 'react';

export interface ReportSectionSkeletonProps {
  /** Type of section to render skeleton for */
  type: 'filter' | 'summary-cards' | 'table' | 'full-page';
  /** Number of cards for summary-cards type */
  cardCount?: number;
  /** Number of table rows for table type */
  rowCount?: number;
  /** Grid columns for summary cards */
  gridColumns?: 1 | 2 | 3 | 4;
  /** Additional CSS classes */
  className?: string;
  /** Card height for summary cards */
  height?: string;
}

/**
 * ReportSectionSkeleton Component
 * 
 * A specialized skeleton component for different sections of report pages.
 * Provides component-level loading states to prevent full page re-renders.
 * Matches the exact layout and styling of actual report components.
 */
const ReportSectionSkeleton: React.FC<ReportSectionSkeletonProps> = ({
  type,
  cardCount = 3,
  rowCount = 5,
  gridColumns = 3,
  className = '',
  height = '130px'
}) => {
  const getGridColumnsClass = () => {
    switch (gridColumns) {
      case 1: return 'grid-cols-1';
      case 2: return 'grid-cols-1 md:grid-cols-2';
      case 3: return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';
      case 4: return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4';
      default: return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';
    }
  };

  const renderFilterSkeleton = () => (
    <div className={`bg-filter p-[1rem] rounded-md animate-pulse ${className}`}>
      {/* Filter Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="w-6 h-6 bg-[#404040] rounded"></div>
          <div className="h-6 bg-[#404040] rounded w-20"></div>
        </div>
        <div className="flex items-center gap-2">
          <div className="h-8 bg-[#404040] rounded w-20"></div>
          <div className="h-8 bg-[#404040] rounded w-24"></div>
        </div>
      </div>
      
      {/* Filter Controls */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {Array.from({ length: 4 }).map((_, index) => (
          <div key={index} className="space-y-2">
            <div className="h-4 bg-[#333333] rounded w-16"></div>
            <div className="h-10 bg-[#404040] rounded"></div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderSummaryCardsSkeleton = () => (
    <div className={`grid ${getGridColumnsClass()} gap-4 ${className}`} style={{ height }}>
      {Array.from({ length: cardCount }).map((_, index) => (
        <div key={index} className="bg-card-general-texture flex gap-[12px] px-[24px] py-[28px] h-full rounded-lg animate-pulse">
          {/* Icon Skeleton */}
          <div className="w-[74px] h-[74px] bg-[#404040] rounded-lg flex-shrink-0"></div>
          
          {/* Content Skeleton */}
          <div className="flex flex-col justify-center space-y-2">
            <div className="h-4 bg-[#333333] rounded w-24"></div>
            <div className="h-8 bg-[#404040] rounded w-32"></div>
          </div>
        </div>
      ))}
    </div>
  );

  const renderTableSkeleton = () => (
    <div className={`bg-filter p-[1rem] rounded-md animate-pulse ${className}`}>
      {/* Table Header */}
      <div className="mb-4">
        <div className="grid grid-cols-6 gap-4 pb-3 border-b border-border-secondary">
          {Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="h-4 bg-[#404040] rounded"></div>
          ))}
        </div>
      </div>
      
      {/* Table Rows */}
      <div className="space-y-3">
        {Array.from({ length: rowCount }).map((_, index) => (
          <div key={index} className="grid grid-cols-6 gap-4 py-3 border-b border-border-secondary/30">
            {Array.from({ length: 6 }).map((_, colIndex) => (
              <div key={colIndex} className="h-4 bg-[#333333] rounded"></div>
            ))}
          </div>
        ))}
      </div>
      
      {/* Pagination Skeleton */}
      <div className="flex items-center justify-between mt-6 pt-4 border-t border-border-secondary">
        <div className="h-4 bg-[#333333] rounded w-32"></div>
        <div className="flex items-center gap-2">
          <div className="h-8 bg-[#404040] rounded w-8"></div>
          <div className="h-8 bg-[#404040] rounded w-8"></div>
          <div className="h-8 bg-[#404040] rounded w-8"></div>
          <div className="h-8 bg-[#404040] rounded w-8"></div>
        </div>
      </div>
    </div>
  );

  const renderFullPageSkeleton = () => (
    <div className={`animate-pulse space-y-6 ${className}`}>
      <div className="h-20 bg-[#404040] rounded mb-6"></div>
      <div className="h-40 bg-[#333333] rounded mb-6"></div>
      <div className="h-96 bg-[#333333] rounded"></div>
    </div>
  );

  switch (type) {
    case 'filter':
      return renderFilterSkeleton();
    case 'summary-cards':
      return renderSummaryCardsSkeleton();
    case 'table':
      return renderTableSkeleton();
    case 'full-page':
      return renderFullPageSkeleton();
    default:
      return renderFullPageSkeleton();
  }
};

export default ReportSectionSkeleton;
